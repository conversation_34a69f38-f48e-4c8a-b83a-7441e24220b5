# API内容解析接口实现说明

## 接口概述

已成功实现异步API内容解析接口 `api_content_ai_parse`，该接口支持两种方式获取API内容并进行智能解析。

## 接口信息

- **路径**: `POST /api/api_auto/content/parse`
- **功能**: API内容进行AI解析
- **位置**: `/Users/<USER>/PycharmProjects/automatrix/apps/ai_api_auto/api.py` (第771-878行)

## 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `oss_id` | string | 可选 | OSS文件ID，与project_id二选一 |
| `project_id` | int | 可选 | 项目ID，与oss_id二选一 |

**重要约束**:
- 两个参数必须传入其中一个，不能同时为空
- 两个参数不能同时传入，必须选择其中一种方式

## 功能实现

### 方式1: 通过OSS ID获取内容

当传入 `oss_id` 时：

1. **验证用户登录状态**
2. **读取OSS文件内容**
   - 使用 `FileUploadService` 从OSS读取文件
   - 自动提取文本内容（支持PDF、Office文档等）
   - 处理文件解码和文本提取错误
3. **调用AI解析**
   - 将文件内容传递给API编排器进行智能解析

### 方式2: 通过项目ID获取YAPI数据

当传入 `project_id` 时：

1. **验证用户登录状态**
2. **查询YAPI接口数据**
   - 从数据库查询该项目下的所有YAPI接口信息
   - 包含接口路径、方法、元数据等完整信息
3. **组装API文档**
   - 将YAPI数据转换为标准化的API文档格式
   - 提取参数、请求体、响应等详细信息
   - 生成符合OpenAPI规范的文档结构
4. **调用AI解析**
   - 将组装后的API文档传递给编排器进行解析

## 核心功能函数

### 1. `_assemble_yapi_content_to_api_document(yapi_apis: list) -> str`

将YAPI接口数据组装成标准化API文档内容：

- **输入**: YAPI接口数据列表
- **输出**: JSON格式的API文档字符串
- **功能**: 
  - 构建文档基础信息（标题、版本、描述等）
  - 遍历所有接口生成端点信息
  - 提取参数、请求体、响应等详细信息
  - 生成符合标准的API文档结构

### 2. `_extract_yapi_parameters(meta_data: dict) -> list`

从YAPI元数据中提取参数信息：

- **路径参数** (`req_params`)
- **查询参数** (`req_query`) 
- **请求头参数** (`req_headers`)
- 自动过滤系统级请求头（如Content-Type、User-Agent）

### 3. `_extract_yapi_request_body(meta_data: dict) -> dict`

提取请求体信息，支持：

- **JSON格式** (`req_body_type: "json"`)
- **表单格式** (`req_body_type: "form"`)
- **原始文本** (`req_body_type: "raw"`)

### 4. `_extract_yapi_responses(meta_data: dict) -> dict`

提取响应信息：

- 成功响应 (200)
- 客户端错误 (400)
- 服务器错误 (500)
- 自动解析响应schema和示例

## 生成的API文档结构

```json
{
  "document_type": "yapi_assembled",
  "title": "YAPI项目接口文档 - 项目ID: {project_id}",
  "description": "从YAPI系统导入的接口文档",
  "version": "1.0.0",
  "base_url": "YAPI基础地址",
  "generated_at": "生成时间",
  "total_endpoints": "端点总数",
  "endpoints": [
    {
      "path": "/api/endpoint",
      "method": "GET|POST|PUT|DELETE",
      "summary": "接口名称",
      "description": "接口描述",
      "operation_id": "操作ID",
      "tags": ["标签"],
      "parameters": [
        {
          "name": "参数名",
          "in": "query|path|header",
          "required": true|false,
          "type": "string|integer|boolean",
          "description": "参数描述",
          "example": "示例值"
        }
      ],
      "request_body": {
        "required": true|false,
        "content_type": "application/json",
        "schema": "请求体schema"
      },
      "responses": {
        "200": {
          "description": "成功响应",
          "content_type": "application/json",
          "schema": "响应schema"
        }
      },
      "raw_yapi_data": "原始YAPI数据"
    }
  ]
}
```

## 响应格式

### 成功响应

```json
{
  "code": 200,
  "message": "API内容解析任务已启动",
  "data": {
    "session_id": "会话ID",
    "content_source": "内容来源描述",
    "content_length": "内容长度",
    "processing_result": "处理结果"
  }
}
```

### 错误响应

```json
{
  "code": 400,
  "message": "错误信息",
  "data": null
}
```

## 使用示例

### 示例1: 使用OSS文件

```bash
curl -X POST "http://localhost:8000/api/api_auto/content/parse" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "oss_id=uploads/2024/08/27/api-doc.pdf"
```

### 示例2: 使用项目ID

```bash
curl -X POST "http://localhost:8000/api/api_auto/content/parse" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "project_id=123"
```

## 错误处理

接口包含完善的错误处理机制：

1. **参数验证错误**: 参数缺失或冲突
2. **用户认证错误**: 用户未登录
3. **文件读取错误**: OSS文件不存在或读取失败
4. **文本提取错误**: 文件格式不支持或内容提取失败
5. **数据查询错误**: 项目下无YAPI数据
6. **编排器错误**: AI解析服务不可用
7. **系统异常**: 其他未预期的错误

## 测试验证

已通过完整的功能测试：

- ✅ 参数提取功能测试通过
- ✅ YAPI内容组装功能测试通过
- ✅ 错误处理机制验证通过
- ✅ 代码语法编译通过
- ✅ 依赖导入验证通过

## 技术特点

1. **双模式支持**: 同时支持文件和数据库两种数据源
2. **智能文本提取**: 自动处理PDF、Office等多种文件格式
3. **标准化输出**: 生成符合OpenAPI规范的文档结构
4. **完善错误处理**: 详细的错误信息和异常处理
5. **异步处理**: 支持大文件和大量接口的异步处理
6. **可扩展性**: 易于扩展支持更多数据源和格式

## 集成说明

该接口已完全集成到现有的API自动化系统中：

- 使用现有的用户认证机制
- 复用文件上传服务和OSS存储
- 集成YAPI数据模型和查询逻辑
- 调用API编排器进行智能解析
- 遵循统一的响应格式规范

接口实现完成，可以直接投入使用！
