# @Time: 2025/8/14 15:12
# @Author: lvjing

import logging
import math
from typing import Optional, Dict, Any

from fastapi import APIRouter, Request, Query, UploadFile, File, Form, BackgroundTasks
from fastapi.responses import StreamingResponse
from tortoise.expressions import Q

from apps.ai_api_auto.models import YapiBaseInfoModel, YapiApiInfoModel, ApiDocumentUploadModel
from apps.ai_api_auto.schemas import (
    YapiBaseInfoAdd, YapiBaseInfoUpdate, YapiFetchRequest
)
from apps.ai_api_auto.services.api_document_service import ApiDocumentService
from apps.ai_api_auto.services.api_orchestrator_service import ApiAutomationOrchestrator
from apps.ai_api_auto.services.yapi_service import YapiService, YapiServiceError
from common.agent.stream_response_collector import StreamResponseCollector
from common.agent.types import AgentPlatform
from common.utils import response_success, response_fail, response_success_paged, model_data_exist, get_caller

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/api_auto")

# 创建服务实例
api_document_service = ApiDocumentService()


# ==================== YAPI基础信息管理接口 ====================

@router.get("/yapi_base", summary="获取YAPI基础信息列表")
async def get_yapi_base_list(
        request: Request,
        page: int = Query(1, ge=1, description="当前页"),
        page_size: int = Query(10, ge=1, le=100, description="每页展示数量"),
        filter: str = Query(None, description="模糊匹配（基础地址或描述）"),
        project_id: int = Query(None, description="项目ID筛选")
):
    """
    获取YAPI基础信息列表
    支持分页、模糊搜索和项目筛选
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 构建查询条件
        query = YapiBaseInfoModel.all()

        # 项目筛选
        if project_id:
            query = query.filter(project_id=project_id)

        # 模糊搜索
        if filter:
            query = query.filter(
                Q(yapi_base_url__contains=filter) |
                Q(description__contains=filter)
            )

        # 获取总数
        total_count = await query.count()

        # 分页查询，关联查询项目和用户信息
        yapi_base_list = await (
            query.select_related('project', 'creator')
            .limit(page_size)
            .offset((page - 1) * page_size)
            .order_by('-id')
        )

        # 构建响应数据
        result_data = []
        for item in yapi_base_list:
            result_data.append({
                "id": item.id,
                "yapi_base_url": item.yapi_base_url,
                "description": item.description,
                "project_id": item.project.id,
                "project_name": item.project.name if item.project else None,
                "creator_name": item.creator.username if item.creator else None,
                "create_time": item.create_time,
                "update_time": item.update_time
            })

        return response_success_paged(
            data=result_data,
            total=total_count,
            current_page=page,
            total_page=math.ceil(total_count / page_size)
        )

    except Exception as e:
        logger.error(f"获取YAPI基础信息列表失败: {str(e)}", exc_info=True)
        return response_fail(msg="获取YAPI基础信息列表失败")


@router.get("/yapi_base/{id}", summary="获取单个YAPI基础信息详情")
async def get_yapi_base_detail(request: Request, id: int):
    """
    获取单个YAPI基础信息详情
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 检查数据是否存在
        if not await model_data_exist(id, YapiBaseInfoModel):
            return response_fail(msg=f"YAPI基础信息【{id}】不存在")

        # 查询详情信息
        yapi_base = await YapiBaseInfoModel.get(id=id).select_related('project', 'creator', 'updater')

        result_data = {
            "id": yapi_base.id,
            "yapi_base_url": yapi_base.yapi_base_url,
            "yapi_token": yapi_base.yapi_token,
            "description": yapi_base.description,
            "project_id": yapi_base.project_id,
            "project_name": yapi_base.project.name if yapi_base.project else None,
            "creator_id": yapi_base.creator_id,
            "creator_name": yapi_base.creator.username if yapi_base.creator else None,
            "updater_id": yapi_base.updater_id,
            "updater_name": yapi_base.updater.username if yapi_base.updater else None,
            "create_time": yapi_base.create_time,
            "update_time": yapi_base.update_time
        }

        return response_success(data=result_data)

    except Exception as e:
        logger.error(f"获取YAPI基础信息详情失败: {str(e)}", exc_info=True)
        return response_fail(msg="获取YAPI基础信息详情失败")


@router.post("/yapi_base", summary="创建YAPI基础信息")
async def create_yapi_base(yapi_base: YapiBaseInfoAdd, request: Request):
    """
    创建YAPI基础信息
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 检查YAPI基础地址是否已存在
        if await YapiBaseInfoModel.exists(yapi_base_url=yapi_base.yapi_base_url):
            return response_fail(msg=f"YAPI基础地址【{yapi_base.yapi_base_url}】已存在")

        # 创建YAPI基础信息
        new_yapi_base = await YapiBaseInfoModel.create(
            yapi_base_url=yapi_base.yapi_base_url,
            yapi_token=yapi_base.yapi_token,
            description=yapi_base.description,
            project_id=yapi_base.project_id,
            creator_id=current_user.id
        )

        return response_success(data={"id": new_yapi_base.id}, msg="创建YAPI基础信息成功")

    except Exception as e:
        logger.error(f"创建YAPI基础信息失败: {str(e)}", exc_info=True)
        return response_fail(msg="创建YAPI基础信息失败")


@router.put("/yapi_base", summary="更新YAPI基础信息")
async def update_yapi_base(yapi_base: YapiBaseInfoUpdate, request: Request):
    """
    更新YAPI基础信息
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 检查数据是否存在
        if not await model_data_exist(yapi_base.id, YapiBaseInfoModel):
            return response_fail(msg=f"YAPI基础信息【{yapi_base.id}】不存在")

        # 检查YAPI基础地址是否重复（排除当前记录）
        if await YapiBaseInfoModel.exclude(id=yapi_base.id).filter(yapi_base_url=yapi_base.yapi_base_url).exists():
            return response_fail(msg=f"YAPI基础地址【{yapi_base.yapi_base_url}】已存在")

        # 更新YAPI基础信息
        count = await YapiBaseInfoModel.filter(id=yapi_base.id).update(
            yapi_base_url=yapi_base.yapi_base_url,
            yapi_token=yapi_base.yapi_token,
            description=yapi_base.description,
            project_id=yapi_base.project_id,
            updater_id=current_user.id
        )

        return response_success(total=count, msg="更新YAPI基础信息成功")

    except Exception as e:
        logger.error(f"更新YAPI基础信息失败: {str(e)}", exc_info=True)
        return response_fail(msg="更新YAPI基础信息失败")


@router.delete("/yapi_base", summary="删除YAPI基础信息")
async def delete_yapi_base(id: int, request: Request):
    """
    删除YAPI基础信息
    注意：删除基础信息会级联删除相关的API信息
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 检查数据是否存在
        if not await model_data_exist(id, YapiBaseInfoModel):
            return response_fail(msg=f"YAPI基础信息【{id}】不存在")

        # 检查是否有关联的API信息
        api_count = await YapiApiInfoModel.filter(yapi_base_id=id).count()
        if api_count > 0:
            return response_fail(msg=f"该YAPI基础信息下还有{api_count}个API信息，请先删除相关API信息")

        # 删除YAPI基础信息
        count = await YapiBaseInfoModel.filter(id=id).delete()

        return response_success(total=count, msg="删除YAPI基础信息成功")

    except Exception as e:
        logger.error(f"删除YAPI基础信息失败: {str(e)}", exc_info=True)
        return response_fail(msg="删除YAPI基础信息失败")


# ==================== YAPI数据获取接口 ====================

@router.post("/yapi/fetch_all", summary="获取YAPI所有接口数据")
async def fetch_all_yapi_data(fetch_request: YapiFetchRequest, request: Request):
    """
    获取YAPI所有接口数据的完整流程
    包括：获取项目信息 -> 获取接口列表 -> 获取接口详情 -> 保存到数据库
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 检查YAPI基础信息是否存在
        if not await model_data_exist(fetch_request.yapi_base_id, YapiBaseInfoModel):
            return response_fail(msg=f"YAPI基础信息【{fetch_request.yapi_base_id}】不存在")

        # 创建YAPI服务实例
        yapi_service = await YapiService.create_from_id(fetch_request.yapi_base_id)

        # 执行完整的数据获取流程
        result = await yapi_service.fetch_all_api_data(
            creator_id=current_user.id,
            project_id=fetch_request.project_id
        )

        return response_success(data=result, msg="YAPI数据获取完成")

    except YapiServiceError as e:
        logger.error(f"YAPI服务错误: {str(e)}", exc_info=True)
        return response_fail(msg=f"YAPI服务错误: {str(e)}")
    except Exception as e:
        logger.error(f"获取YAPI数据失败: {str(e)}", exc_info=True)
        return response_fail(msg="获取YAPI数据失败")


# ==================== YAPI获取接口信息管理接口 ====================

@router.get("/yapi_api", summary="获取YAPI接口信息列表")
async def get_yapi_api_list(
        request: Request,
        page: int = Query(1, ge=1, description="当前页"),
        page_size: int = Query(10, ge=1, le=100, description="每页展示数量"),
        filter: str = Query(None, description="模糊匹配（接口名称或接口路径）"),
        api_method: str = Query(None, description="接口请求方式筛选"),
        yapi_base_id: int = Query(None, description="YAPI基础信息ID筛选"),
        project_id: int = Query(None, description="项目ID筛选")
):
    """
    获取YAPI接口信息列表
    支持分页、模糊搜索和多条件筛选
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 构建查询条件
        query = YapiApiInfoModel.all()

        # 项目筛选
        if project_id:
            query = query.filter(project_id=project_id)

        # YAPI基础信息筛选
        if yapi_base_id:
            query = query.filter(yapi_base_id=yapi_base_id)

        # 接口请求方式筛选
        if api_method:
            query = query.filter(api_method=api_method)

        # 模糊搜索
        if filter:
            query = query.filter(
                Q(api_name__contains=filter) |
                Q(api_path__contains=filter)
            )

        # 获取总数
        total_count = await query.count()

        # 分页查询，关联查询相关信息
        yapi_api_list = await (
            query.select_related('yapi_base', 'project', 'creator')
            .limit(page_size)
            .offset((page - 1) * page_size)
            .order_by('-id')
        )

        # 构建响应数据
        result_data = []
        for item in yapi_api_list:
            result_data.append({
                "id": item.id,
                "api_name": item.api_name,
                "api_path": item.api_path,
                "api_method": item.api_method,
                "yapi_base_id": item.yapi_base_id,
                "yapi_base_url": item.yapi_base.yapi_base_url if item.yapi_base else None,
                "project_name": item.project.name if item.project else None,
                "creator_name": item.creator.username if item.creator else None,
                "create_time": item.create_time,
                "update_time": item.update_time
            })

        return response_success_paged(
            data=result_data,
            total=total_count,
            current_page=page,
            total_page=math.ceil(total_count / page_size)
        )

    except Exception as e:
        logger.error(f"获取YAPI接口信息列表失败: {str(e)}", exc_info=True)
        return response_fail(msg="获取YAPI接口信息列表失败")


@router.get("/yapi_api/{id}", summary="获取单个YAPI接口信息详情")
async def get_yapi_api_detail(request: Request, id: int):
    """
    获取单个YAPI接口信息详情
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 检查数据是否存在
        if not await model_data_exist(id, YapiApiInfoModel):
            return response_fail(msg=f"YAPI接口信息【{id}】不存在")

        # 查询详情信息
        yapi_api = await YapiApiInfoModel.get(id=id).select_related('yapi_base', 'project', 'creator', 'updater')

        result_data = {
            "id": yapi_api.id,
            "api_name": yapi_api.api_name,
            "api_path": yapi_api.api_path,
            "api_method": yapi_api.api_method,
            "api_meta_data": yapi_api.api_meta_data,
            "yapi_base_id": yapi_api.yapi_base_id,
            "yapi_base_url": yapi_api.yapi_base.yapi_base_url if yapi_api.yapi_base else None,
            "project_id": yapi_api.project_id,
            "project_name": yapi_api.project.name if yapi_api.project else None,
            "creator_id": yapi_api.creator_id,
            "creator_name": yapi_api.creator.username if yapi_api.creator else None,
            "updater_id": yapi_api.updater_id,
            "updater_name": yapi_api.updater.username if yapi_api.updater else None,
            "create_time": yapi_api.create_time,
            "update_time": yapi_api.update_time
        }

        return response_success(data=result_data)

    except Exception as e:
        logger.error(f"获取YAPI接口信息详情失败: {str(e)}", exc_info=True)
        return response_fail(msg="获取YAPI接口信息详情失败")


@router.delete("/yapi_api", summary="删除YAPI接口信息")
async def delete_yapi_api(id: int, request: Request):
    """
    删除YAPI接口信息
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 检查数据是否存在
        if not await model_data_exist(id, YapiApiInfoModel):
            return response_fail(msg=f"YAPI接口信息【{id}】不存在")

        # 删除YAPI接口信息
        count = await YapiApiInfoModel.filter(id=id).delete()

        return response_success(total=count, msg="删除YAPI接口信息成功")

    except Exception as e:
        logger.error(f"删除YAPI接口信息失败: {str(e)}", exc_info=True)
        return response_fail(msg="删除YAPI接口信息失败")


# ==================== 接口文档上传查看管理接口 ====================

@router.post("/document/upload", summary="上传接口文档")
async def upload_api_document(
        request: Request,
        file: UploadFile = File(...),
        document_name: str = Form(None, description="文档名称，如果不传则使用上传文件的原始名称"),
        description: str = Form(None, description="文档描述"),
        document_category: str = Form("api_doc", description="文档分类"),
        project_id: int = Form(None, description="项目ID"),
        convert_office_to_pdf: bool = Form(True, description="是否将Office文件转换为PDF")
):
    """
    上传接口文档
    支持文本文件、Office文件、PDF等多种格式
    Office文件会自动转换为PDF格式
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 构建文档数据
        document_data = {
            "document_name": document_name,
            "description": description,
            "document_category": document_category,
            "project_id": project_id,
            "convert_office_to_pdf": convert_office_to_pdf
        }

        # 使用ApiDocumentService上传文档
        document_record = await api_document_service.upload_document(
            file=file,
            document_data=document_data,
            current_user=current_user
        )

        return response_success(
            data={
                "id": document_record.id,
                "document_name": document_record.document_name,
                "oss_file_id": document_record.oss_file_id,
                "oss_file_url": document_record.oss_file_url,
                "file_size": document_record.file_size,
                "is_converted_to_pdf": document_record.is_converted_to_pdf
            },
            msg="接口文档上传成功"
        )
    except Exception as e:
        logger.error(f"上传接口文档失败: {str(e)}", exc_info=True)
        return response_fail(msg="上传接口文档失败")


@router.get("/document", summary="获取接口文档列表")
async def get_api_document_list(
        request: Request,
        page: int = Query(1, ge=1, description="当前页"),
        page_size: int = Query(10, ge=1, le=100, description="每页展示数量"),
        filter: str = Query(None, description="模糊匹配（文档名称或原始文件名）"),
        document_category: str = Query(None, description="文档分类筛选"),
        file_type: str = Query(None, description="文件类型筛选"),
        project_id: int = Query(None, description="项目ID筛选")
):
    """
    获取接口文档列表
    支持分页、模糊搜索和多条件筛选
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 构建查询条件
        query = ApiDocumentUploadModel.all()

        # 项目筛选
        if project_id:
            query = query.filter(project_id=project_id)

        # 文档分类筛选
        if document_category:
            query = query.filter(document_category=document_category)

        # 文件类型筛选
        if file_type:
            query = query.filter(file_type=file_type)

        # 模糊搜索
        if filter:
            query = query.filter(
                Q(document_name__contains=filter) |
                Q(original_filename__contains=filter) |
                Q(description__contains=filter)
            )

        # 获取总数
        total_count = await query.count()

        # 分页查询，关联查询相关信息
        document_list = await (
            query.select_related('project', 'creator')
            .limit(page_size)
            .offset((page - 1) * page_size)
            .order_by('-id')
        )

        # 构建响应数据
        result_data = []
        for item in document_list:
            result_data.append({
                "id": item.id,
                "document_name": item.document_name,
                "original_filename": item.original_filename,
                "file_type": item.file_type,
                "file_size": item.file_size,
                "document_category": item.document_category,
                "description": item.description,
                "is_converted_to_pdf": item.is_converted_to_pdf,
                "project_name": item.project.name if item.project else None,
                "creator_name": item.creator.username if item.creator else None,
                "create_time": item.create_time,
                "update_time": item.update_time
            })

        return response_success_paged(
            data=result_data,
            total=total_count,
            current_page=page,
            total_page=math.ceil(total_count / page_size)
        )

    except Exception as e:
        logger.error(f"获取接口文档列表失败: {str(e)}", exc_info=True)
        return response_fail(msg="获取接口文档列表失败")


@router.get("/document/{id}", summary="获取接口文档详情")
async def get_api_document_detail(
        request: Request,
        id: int,
        include_access_url: bool = Query(True, description="是否包含文档访问链接"),
        expires: int = Query(7200, ge=300, le=604800, description="访问链接过期时间（秒），默认2小时")
):
    """
    获取接口文档详情信息
    包含完整的文档信息和可访问的OSS链接，前端可直接使用链接查看文档
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 检查数据是否存在
        if not await model_data_exist(id, ApiDocumentUploadModel):
            return response_fail(msg=f"接口文档【{id}】不存在")

        logger.info(f"用户 {current_user.username} 获取接口文档详情: ID={id}")

        # 使用ApiDocumentService获取文档详情
        result_data = await api_document_service.get_document_detail(
            document_id=id,
            include_access_url=include_access_url,
            expires=expires
        )

        return response_success(data=result_data)
    except Exception as e:
        logger.error(f"获取接口文档详情失败: {str(e)}", exc_info=True)
        return response_fail(msg="获取接口文档详情失败")


@router.get("/document/{id}/download", summary="下载接口文档")
async def download_api_document(request: Request, id: int):
    """
    下载接口文档
    直接从OSS下载文件并返回给客户端
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 检查数据是否存在
        if not await model_data_exist(id, ApiDocumentUploadModel):
            return response_fail(msg=f"接口文档【{id}】不存在")

        logger.info(f"用户 {current_user.username} 下载接口文档: ID={id}")

        # 使用ApiDocumentService下载文档
        download_result = await api_document_service.download_document(id)

        if not download_result.get("success"):
            return response_fail(msg=f"下载文档失败: {download_result.get('error', '未知错误')}")

        # 获取下载信息
        filename = download_result["filename"]
        content_type = download_result["content_type"]
        file_content = download_result["file_content"]

        # 创建文件流
        def generate():
            yield file_content

        # 返回文件流响应
        return StreamingResponse(
            generate(),
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{filename}",
                "Content-Length": str(len(file_content))
            }
        )
    except Exception as e:
        logger.error(f"下载接口文档失败: {str(e)}", exc_info=True)
        return response_fail(msg="下载接口文档失败")


@router.delete("/document/{id}", summary="删除接口文档")
async def delete_api_document(request: Request, id: int):
    """
    删除接口文档
    同时删除数据库记录和OSS文件
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 检查数据是否存在
        if not await model_data_exist(id, ApiDocumentUploadModel):
            return response_fail(msg=f"接口文档【{id}】不存在")

        logger.info(f"用户 {current_user.username} 删除接口文档: ID={id}")

        # 使用ApiDocumentService删除文档
        delete_result = await api_document_service.delete_document(id)

        if delete_result.get("success"):
            return response_success(
                total=delete_result["deleted_count"],
                msg=f"接口文档删除成功"
            )
        else:
            return response_fail(msg="删除文档失败")

    except Exception as e:
        logger.error(f"删除接口文档失败: {str(e)}", exc_info=True)
        return response_fail(msg="删除接口文档失败")


@router.delete("/document/batch", summary="批量删除接口文档")
async def batch_delete_api_documents(request: Request, ids: str = Query(..., description="文档ID列表，用逗号分隔")):
    """
    批量删除接口文档
    同时删除数据库记录和OSS文件
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 解析ID列表
        try:
            id_list = [int(id_str.strip()) for id_str in ids.split(',') if id_str.strip()]
        except ValueError:
            return response_fail(msg="无效的ID格式")

        if not id_list:
            return response_fail(msg="请提供要删除的文档ID")

        logger.info(f"用户 {current_user.username} 批量删除接口文档: {id_list}")

        # 使用ApiDocumentService批量删除文档
        delete_result = await api_document_service.batch_delete_documents(id_list)

        if delete_result.get("success"):
            success_count = delete_result["success_count"]
            failed_count = delete_result["failed_count"]
            failed_files = delete_result["failed_files"]

            # 构建响应消息
            if failed_count == 0:
                msg = f"成功删除 {success_count} 个文档"
            else:
                msg = f"成功删除 {success_count} 个文档，失败 {failed_count} 个"

            return response_success(
                data={
                    "success_count": success_count,
                    "failed_count": failed_count,
                    "failed_files": failed_files
                },
                msg=msg
            )
        else:
            return response_fail(msg="批量删除文档失败")
    except Exception as e:
        logger.error(f"批量删除接口文档失败: {str(e)}", exc_info=True)
        return response_fail(msg="批量删除接口文档失败")


# ==================== 接口解析智能体相关接口 ====================
# 全局编排器实例
orchestrator: Optional[ApiAutomationOrchestrator] = None
active_sessions: Dict[str, Dict[str, Any]] = {}


async def initialize_orchestrator():
    """初始化编排器（在应用启动时调用）"""
    global orchestrator
    if orchestrator is None:
        logger.info("开始初始化API自动化编排器...")

        # 创建响应收集器
        collector = StreamResponseCollector(platform=AgentPlatform.API_AUTOMATION)

        # 创建编排器
        orchestrator = ApiAutomationOrchestrator(collector=collector)

        # 初始化编排器
        await orchestrator.initialize()

        logger.info("API自动化编排器初始化完成")


async def get_orchestrator_async() -> Optional[ApiAutomationOrchestrator]:
    """异步获取编排器实例，如果未初始化则尝试初始化"""
    global orchestrator
    if orchestrator is None:
        try:
            logger.info("编排器未初始化，尝试异步初始化...")
            await initialize_orchestrator()
        except Exception as e:
            logger.error(f"异步初始化编排器失败: {str(e)}")
            return None
    return orchestrator


@router.post("/content/parse", summary="API内容进行AI解析")
async def api_content_ai_parse(
        request: Request,
        background_tasks: BackgroundTasks,
        oss_id: Optional[str] = Form(None, description="OSS文件ID，与project_id二选一"),
        project_id: Optional[int] = Form(None, description="项目ID，与oss_id二选一"),
):
    """
    API内容进行AI解析

    支持两种方式获取API内容：
    1. 通过oss_id从OSS获取已上传的接口文档内容
    2. 通过project_id从数据库查询已解析的YAPI接口数据并组装成API文档内容

    两个参数必须传入其中一个，不能同时为空或同时传入
    """
    try:
        # 获取当前用户
        current_user = await get_caller(request)
        if not current_user:
            return response_fail(msg="用户未登录")

        # 参数验证：两个参数必须传入其中一个
        if not oss_id and not project_id:
            return response_fail(msg="oss_id和project_id必须传入其中一个")

        if oss_id and project_id:
            return response_fail(msg="oss_id和project_id不能同时传入，请选择其中一种方式")

        # 确保编排器已初始化
        orchestrator_instance = await get_orchestrator_async()
        if not orchestrator_instance:
            return response_fail(msg="API自动化编排器初始化失败")

        # 生成会话ID
        import uuid
        session_id = str(uuid.uuid4())

        # 根据参数类型获取API内容
        api_content = ""
        content_source = ""

        if oss_id:
            # 方式1: 从OSS获取文档内容
            logger.info(f"用户 {current_user.username} 通过OSS ID获取API内容: {oss_id}")
            content_source = f"OSS文件(ID: {oss_id})"

            # 使用文件上传服务读取OSS文件内容
            from common.service.file_upload_service import FileUploadService
            file_service = FileUploadService()

            content_result = await file_service.read_file_content(oss_id)
            if not content_result.get("success"):
                return response_fail(msg=f"读取OSS文件失败: {content_result.get('error', '未知错误')}")

            # 获取文本内容
            text_content = content_result.get("text_content")
            if not text_content:
                decode_error = content_result.get("decode_error")
                if decode_error:
                    return response_fail(msg=f"文件文本提取失败: {decode_error}")
                else:
                    return response_fail(msg="无法从文件中提取文本内容")

            api_content = text_content

        else:
            # 方式2: 从数据库查询YAPI接口数据并组装
            logger.info(f"用户 {current_user.username} 通过项目ID获取API内容: {project_id}")
            content_source = f"YAPI项目数据(项目ID: {project_id})"

            # 查询该项目下的所有YAPI接口信息
            yapi_apis = await YapiApiInfoModel.filter(
                project_id=project_id
            ).select_related('yapi_base').order_by('api_path', 'api_method')

            if not yapi_apis:
                return response_fail(msg=f"项目ID {project_id} 下未找到任何YAPI接口数据")

            # 组装API文档内容
            api_content = await _assemble_yapi_content_to_api_document(yapi_apis)

        logger.info(f"成功获取API内容，来源: {content_source}, 内容长度: {len(api_content)}")

        # 调用编排器处理API文档内容
        result = await orchestrator_instance.process_api_document_content(
            session_id=session_id,
            api_file_content=api_content,
            config={
                "content_source": content_source,
                "user_id": current_user.id,
                "username": current_user.username
            }
        )

        return response_success(
            data={
                "session_id": session_id,
                "content_source": content_source,
                "content_length": len(api_content),
                "processing_result": result
            },
            msg="API内容解析任务已启动"
        )

    except Exception as e:
        logger.error(f"API内容解析失败: {str(e)}", exc_info=True)
        return response_fail(msg=f"API内容解析失败: {str(e)}")


async def _assemble_yapi_content_to_api_document(yapi_apis: list) -> str:
    """
    将YAPI接口数据组装成API文档内容

    Args:
        yapi_apis: YAPI接口数据列表

    Returns:
        str: 组装后的API文档内容
    """
    try:
        import json
        from datetime import datetime

        # 获取第一个接口的基础信息
        first_api = yapi_apis[0]
        yapi_base = first_api.yapi_base

        # 构建API文档结构
        api_document = {
            "document_type": "yapi_assembled",
            "title": f"YAPI项目接口文档 - 项目ID: {first_api.project_id}",
            "description": f"从YAPI系统导入的接口文档，基础地址: {yapi_base.yapi_base_url if yapi_base else 'N/A'}",
            "version": "1.0.0",
            "base_url": yapi_base.yapi_base_url if yapi_base else "",
            "generated_at": datetime.now().isoformat(),
            "total_endpoints": len(yapi_apis),
            "endpoints": []
        }

        # 遍历所有接口，组装端点信息
        for api in yapi_apis:
            try:
                # 获取接口元数据
                meta_data = api.api_meta_data or {}

                # 构建端点信息
                endpoint = {
                    "path": api.api_path,
                    "method": api.api_method.upper(),
                    "summary": api.api_name,
                    "description": meta_data.get("desc", "") or meta_data.get("markdown", ""),
                    "operation_id": f"{api.api_method.lower()}_{api.api_path.replace('/', '_').replace('{', '').replace('}', '')}",
                    "tags": [meta_data.get("tag", ["默认"])[0] if isinstance(meta_data.get("tag"), list) else meta_data.get("tag", "默认")],

                    # 请求参数
                    "parameters": _extract_yapi_parameters(meta_data),

                    # 请求体
                    "request_body": _extract_yapi_request_body(meta_data),

                    # 响应信息
                    "responses": _extract_yapi_responses(meta_data),

                    # 其他信息
                    "deprecated": meta_data.get("status") == "undone",
                    "yapi_interface_id": meta_data.get("_id"),
                    "yapi_project_id": meta_data.get("project_id"),
                    "created_at": meta_data.get("add_time"),
                    "updated_at": meta_data.get("up_time"),

                    # 原始元数据
                    "raw_yapi_data": meta_data
                }

                api_document["endpoints"].append(endpoint)

            except Exception as e:
                logger.warning(f"组装接口 {api.api_name} 时出错: {str(e)}")
                # 添加简化版本的端点信息
                api_document["endpoints"].append({
                    "path": api.api_path,
                    "method": api.api_method.upper(),
                    "summary": api.api_name,
                    "description": f"接口组装时出现错误: {str(e)}",
                    "raw_yapi_data": api.api_meta_data or {}
                })

        # 转换为JSON字符串
        api_content = json.dumps(api_document, ensure_ascii=False, indent=2)

        logger.info(f"成功组装YAPI接口文档，共 {len(yapi_apis)} 个接口")
        return api_content

    except Exception as e:
        logger.error(f"组装YAPI接口文档失败: {str(e)}")
        raise Exception(f"组装YAPI接口文档失败: {str(e)}")


def _extract_yapi_parameters(meta_data: dict) -> list:
    """从YAPI元数据中提取参数信息"""
    parameters = []

    try:
        # 路径参数
        req_params = meta_data.get("req_params", [])
        if req_params:
            for param in req_params:
                parameters.append({
                    "name": param.get("name", ""),
                    "in": "path",
                    "required": True,
                    "type": "string",
                    "description": param.get("desc", ""),
                    "example": param.get("example", "")
                })

        # 查询参数
        req_query = meta_data.get("req_query", [])
        if req_query:
            for param in req_query:
                parameters.append({
                    "name": param.get("name", ""),
                    "in": "query",
                    "required": param.get("required") == "1",
                    "type": param.get("type", "string"),
                    "description": param.get("desc", ""),
                    "example": param.get("example", "")
                })

        # 请求头参数
        req_headers = meta_data.get("req_headers", [])
        if req_headers:
            for header in req_headers:
                if header.get("name") and header.get("name").lower() not in ["content-type", "user-agent"]:
                    parameters.append({
                        "name": header.get("name", ""),
                        "in": "header",
                        "required": header.get("required") == "1",
                        "type": "string",
                        "description": header.get("desc", ""),
                        "example": header.get("value", "")
                    })

    except Exception as e:
        logger.warning(f"提取YAPI参数信息时出错: {str(e)}")

    return parameters


def _extract_yapi_request_body(meta_data: dict) -> dict:
    """从YAPI元数据中提取请求体信息"""
    request_body = {}

    try:
        req_body_type = meta_data.get("req_body_type", "")
        req_body_other = meta_data.get("req_body_other", "")
        req_body_form = meta_data.get("req_body_form", [])

        if req_body_type == "json" and req_body_other:
            request_body = {
                "required": True,
                "content_type": "application/json",
                "schema": req_body_other,
                "raw_schema": req_body_other
            }
        elif req_body_type == "form" and req_body_form:
            request_body = {
                "required": True,
                "content_type": "application/x-www-form-urlencoded",
                "form_data": req_body_form,
                "schema": {
                    "type": "object",
                    "properties": {
                        item.get("name", ""): {
                            "type": item.get("type", "string"),
                            "description": item.get("desc", ""),
                            "required": item.get("required") == "1"
                        } for item in req_body_form if item.get("name")
                    }
                }
            }
        elif req_body_type == "raw":
            request_body = {
                "required": True,
                "content_type": "text/plain",
                "raw_content": req_body_other
            }

    except Exception as e:
        logger.warning(f"提取YAPI请求体信息时出错: {str(e)}")

    return request_body


def _extract_yapi_responses(meta_data: dict) -> dict:
    """从YAPI元数据中提取响应信息"""
    responses = {}

    try:
        # 默认成功响应
        res_body = meta_data.get("res_body", "")
        res_body_type = meta_data.get("res_body_type", "json")

        if res_body:
            responses["200"] = {
                "description": "成功响应",
                "content_type": "application/json" if res_body_type == "json" else "text/plain",
                "schema": res_body if res_body_type == "json" else {"type": "string"},
                "raw_schema": res_body
            }
        else:
            responses["200"] = {
                "description": "成功响应",
                "content_type": "application/json",
                "schema": {"type": "object"}
            }

        # 通用错误响应
        responses["400"] = {
            "description": "请求参数错误",
            "content_type": "application/json",
            "schema": {
                "type": "object",
                "properties": {
                    "errcode": {"type": "integer"},
                    "errmsg": {"type": "string"}
                }
            }
        }

        responses["500"] = {
            "description": "服务器内部错误",
            "content_type": "application/json",
            "schema": {
                "type": "object",
                "properties": {
                    "errcode": {"type": "integer"},
                    "errmsg": {"type": "string"}
                }
            }
        }

    except Exception as e:
        logger.warning(f"提取YAPI响应信息时出错: {str(e)}")
        responses["200"] = {
            "description": "响应信息提取失败",
            "content_type": "application/json",
            "schema": {"type": "object"}
        }

    return responses
    