# @Time: 2025/8/25 17:27
# @Author: lvjing
"""
智能体工厂
企业级智能体管理系统，统一创建和管理API自动化测试智能体
"""
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional, Type, List

from autogen_agentchat.agents import AssistantAgent
from autogen_core import SingleThreadedAgentRuntime, ClosureAgent, TypeSubscription

from apps.ai_api_auto.prompts.api_data_persistence_prompt import API_DATA_PERSISTENCE_CONFIG
from apps.ai_api_auto.prompts.api_doc_content_parser_prompt import API_DOC_PARSER_CONFIG
from common.agent.types import AgentTypes, AgentPlatform, AGENT_NAMES, TopicTypes

logger = logging.getLogger(__name__)


class AgentFactory:
    """
    企业级智能体工厂

    专注于API自动化测试场景的智能体管理，提供：
    1. AssistantAgent 和自定义智能体的统一创建
    2. 智能体配置的集中管理
    3. 运行时注册和生命周期管理
    4. 企业级的错误处理和日志记录
    """

    _instance = None
    _initialized = False

    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化智能体工厂"""
        if self._initialized:
            return

        # 核心存储
        self._agent_classes: Dict[str, Type] = {}
        self._registered_agents: Dict[str, Dict[str, Any]] = {}
        self._runtime_agents: Dict[str, Dict[str, Any]] = {}

        # 运行时管理
        self._runtime: Optional[SingleThreadedAgentRuntime] = None

        # 模型客户端（用于创建智能体）
        self.model_client = None

        # 智能体配置
        self.agent_config = {}

        # 创建时间（用于监控）
        self.creation_time = datetime.now()

        # 初始化
        self._register_api_automation_agents()
        self._initialized = True
        logger.info("API自动化智能体工厂初始化完成")

    def _register_api_automation_agents(self) -> None:
        """注册API自动化智能体类 - 重新设计版本"""
        try:
            # 导入重新设计的API自动化智能体
            from apps.ai_api_auto.agents.api_docs_paraser_agent import ApiDocParserAgent
            from apps.ai_api_auto.agents.api_data_persistence_agent import ApiDataPersistenceAgent

            # 注册智能体类
            self._agent_classes.update({
                AgentTypes.API_DOC_PARSER.value: ApiDocParserAgent,
                AgentTypes.API_DATA_PERSISTENCE.value: ApiDataPersistenceAgent,
            })

            logger.info(f"已注册 {len(self._agent_classes)} 个API自动化智能体类")
            logger.info(f"注册的智能体类型: {list(self._agent_classes.keys())}")

        except ImportError as e:
            logger.error(f"API自动化智能体导入失败: {str(e)}")
            raise

        except Exception as e:
            logger.error(f"注册API自动化智能体失败: {str(e)}")
            raise

    def _get_agent_config(self, agent_type: str) -> Dict[str, Any]:
        """获取智能体配置

        Args:
            agent_type: 智能体类型

        Returns:
            Dict[str, Any]: 智能体配置
        """
        # API自动化智能体配置
        configs = {
            AgentTypes.API_DOC_PARSER.value: API_DOC_PARSER_CONFIG,
            AgentTypes.API_DATA_PERSISTENCE.value: API_DATA_PERSISTENCE_CONFIG
        }
        return configs.get(agent_type, {
            "name": agent_type,
            "description": f"智能体: {agent_type}",
            "capabilities": [],
            "system_message": "你是一个专业的AI助手，请根据用户需求提供帮助。"
        })

    # ===============================创建智能体===============================
    async def create_agent(
            self,
            agent_type: str,
            platform: AgentPlatform = AgentPlatform.API_AUTOMATION,
            model_client_instance=None,
            **kwargs
    ) -> Any:
        """创建智能体实例

        Args:
            agent_type: 智能体类型
            platform: 智能体平台类型
            model_client_instance: 大模型客户端实例
            **kwargs: 其他参数

        Returns:
            智能体实例

        Raises:
            ValueError: 当智能体类型不支持时抛出异常
        """
        # 根据平台类型创建智能体
        if platform == AgentPlatform.AUTO_GEN:
            return await self._create_autogen_agent(agent_type, model_client_instance, **kwargs)
        elif platform == AgentPlatform.API_AUTOMATION:
            return await self._create_api_automation_agent(agent_type, model_client_instance, **kwargs)
        else:
            raise ValueError(f"不支持的智能体平台: {platform}")

    async def _create_autogen_agent(
            self,
            agent_type: str,
            model_client_instance=None,
            **kwargs
    ) -> AssistantAgent:
        """创建AutoGen AssistantAgent

        Args:
            agent_type: 智能体类型
            model_client_instance: 大模型客户端实例
            **kwargs: 其他参数

        Returns:
            AssistantAgent实例
        """
        try:
            from common.llms import get_model_client

            # 获取模型客户端
            model_client = model_client_instance or get_model_client("deepseek", max_tokens=8192)

            # 获取智能体配置
            agent_config = self._get_agent_config(agent_type)

            # 创建AssistantAgent
            agent = AssistantAgent(
                name=agent_config["name"],
                model_client=model_client,
                system_message=agent_config["system_message"],
                description=agent_config["description"],
                **kwargs
            )

            # 注册智能体
            self._register_agent(
                agent_type=agent_type,
                agent_instance=agent,
                platform=AgentPlatform.AUTO_GEN,
                config=agent_config
            )

            logger.info(f"✅ AutoGen智能体创建成功: {agent_type}")
            return agent

        except Exception as e:
            logger.error(f"❌ AutoGen智能体创建失败: {agent_type}, 错误: {str(e)}")
            raise

    async def _create_api_automation_agent(
            self,
            agent_type: str,
            model_client_instance=None,
            **kwargs
    ):
        """创建API自动化智能体

        Args:
            agent_type: 智能体类型
            model_client_instance: 大模型客户端实例
            **kwargs: 其他参数

        Returns:
            BaseAgent实例
        """
        if agent_type not in self._agent_classes:
            raise ValueError(f"未知的智能体类型: {agent_type}")

        # 获取智能体类和名称
        agent_class = self._agent_classes[agent_type]
        agent_name = AGENT_NAMES.get(agent_type, agent_type)

        try:
            # 获取智能体配置
            agent_config = self._get_agent_config(agent_type)

            # 创建智能体实例
            agent = agent_class(
                agent_id=agent_type,
                agent_name=agent_name,
                model_client_instance=model_client_instance,
                agent_config=agent_config,
                **kwargs
            )

            # 注册智能体
            self._register_agent(
                agent_type=agent_type,
                agent_instance=agent,
                platform=AgentPlatform.API_AUTOMATION,
                config=agent_config
            )

            logger.info(f"✅ API自动化智能体创建成功: {agent_type}")
            return agent

        except Exception as e:
            logger.error(f"❌ API自动化智能体创建失败: {agent_type}, 错误: {str(e)}")
            raise

    # ===============================注册智能体===============================
    def _register_agent(
            self,
            agent_type: str,
            agent_instance: Any,
            platform: AgentPlatform,
            config: Dict[str, Any]
    ) -> None:
        """注册智能体实例

        Args:
            agent_type: 智能体类型
            agent_instance: 智能体实例
            platform: 智能体平台
            config: 智能体配置
        """
        self._registered_agents[agent_type] = {
            "instance": agent_instance,
            "platform": platform,
            "config": config,
            "created_at": datetime.now()
        }
        logger.debug(f"智能体注册成功: {agent_type} ({platform.value})")

    async def register_to_runtime(self, runtime: SingleThreadedAgentRuntime) -> None:
        """将所有智能体注册到运行时

        Args:
            runtime: AutoGen运行时实例
        """
        self._runtime = runtime

        try:
            # 创建并注册所有智能体到运行时
            for agent_type in self._agent_classes.keys():
                # 获取对应的topic_type
                topic_type = self._get_topic_type_for_agent(agent_type)

                await self.register_agent_to_runtime(
                    runtime=runtime,
                    agent_type=agent_type,
                    topic_type=topic_type
                )

            logger.info(f"已注册 {len(self._agent_classes)} 个智能体到运行时")

        except Exception as e:
            logger.error(f"批量注册智能体到运行时失败: {str(e)}")
            raise

    def _get_topic_type_for_agent(self, agent_type: str) -> str:
        """获取智能体对应的主题类型

        Args:
            agent_type: 智能体类型

        Returns:
            str: 主题类型
        """
        # 智能体类型到主题类型的映射
        topic_mapping = {
            AgentTypes.API_DOC_PARSER.value: TopicTypes.API_DOC_PARSER.value,
        }

        return topic_mapping.get(agent_type, agent_type)

    async def register_agent_to_runtime(self,
                                        runtime: SingleThreadedAgentRuntime,
                                        agent_type: str,
                                        topic_type: str,
                                        **kwargs) -> None:
        """注册单个智能体到运行时

        Args:
            runtime: 智能体运行时
            agent_type: 智能体类型
            topic_type: 主题类型
            **kwargs: 智能体初始化参数
        """
        try:
            if agent_type not in self._agent_classes:
                raise ValueError(f"未知的智能体类型: {agent_type}")

            agent_class = self._agent_classes[agent_type]

            # 直接注册智能体类到运行时，使用 agent_type 作为 agent_id
            await agent_class.register(
                runtime,
                agent_type,
                lambda: self.create_agent(agent_type, **kwargs)
            )

            # 记录运行时注册信息
            self._runtime_agents[agent_type] = {
                "agent_type": agent_type,
                "topic_type": topic_type,
                "agent_name": AGENT_NAMES.get(agent_type, agent_type),
                "kwargs": kwargs,
                "registered_at": datetime.now(),
                "status": "registered"
            }

            logger.info(f"智能体注册到运行时成功: {AGENT_NAMES.get(agent_type, agent_type)} -> {topic_type}")

        except Exception as e:
            logger.error(f"注册智能体到运行时失败: {agent_type}, 错误: {str(e)}")
            raise

    def get_runtime_agent_info(self, agent_type: str) -> Optional[Dict[str, Any]]:
        """获取运行时智能体信息

        Args:
            agent_type: 智能体类型

        Returns:
            运行时智能体信息或None
        """
        return self._runtime_agents.get(agent_type)

    def list_runtime_agents(self) -> List[str]:
        """列出所有已注册到运行时的智能体类型

        Returns:
            已注册到运行时的智能体类型列表
        """
        return list(self._runtime_agents.keys())

    async def register_agents_to_runtime(self, runtime):
        """将智能体注册到运行时 - 企业级实现

        Args:
            runtime: SingleThreadedAgentRuntime 实例
        """
        global agent_type
        registration_start = time.time()

        try:
            logger.info("🚀 开始注册API自动化智能体到运行时...")
            self._runtime = runtime

            # 定义需要注册的智能体配置
            agent_registrations = [
                {
                    "agent_type": AgentTypes.API_DOC_PARSER.value,
                    "topic_type": TopicTypes.API_DOC_PARSER.value,
                },
                {
                    "agent_type": AgentTypes.API_DATA_PERSISTENCE.value,
                    "topic_type": TopicTypes.API_DATA_PERSISTENCE.value,
                },
            ]

            # 批量注册智能体
            successful_registrations = 0
            failed_registrations = []

            for registration in agent_registrations:
                try:
                    agent_type = registration["agent_type"]
                    topic_type = registration["topic_type"]

                    # 只注册已成功导入的智能体类
                    if agent_type in self._agent_classes:
                        await self.register_agent_to_runtime(
                            runtime=runtime,
                            agent_type=agent_type,
                            topic_type=topic_type
                        )
                        successful_registrations += 1
                    else:
                        failed_registrations.append(agent_type)
                        logger.warning(f"跳过未导入的智能体: {agent_type}")

                except Exception as e:
                    failed_registrations.append(agent_type)
                    logger.error(f"注册智能体失败: {agent_type} - {str(e)}")

            # 更新统计信息
            registration_time = time.time() - registration_start

            logger.info(f"✅ API自动化智能体注册完成: 成功 {successful_registrations} 个, "
                        f"跳过 {len(failed_registrations)} 个, 耗时 {registration_time:.2f}s")

            if failed_registrations:
                logger.warning(f"未注册的智能体: {failed_registrations}")

        except Exception as e:
            registration_time = time.time() - registration_start
            logger.error(f"❌ 智能体注册到运行时失败 (耗时 {registration_time:.2f}s): {str(e)}")
            raise

    async def register_stream_collector(self, runtime, collector):
        """注册流式响应收集器

        Args:
            runtime: SingleThreadedAgentRuntime 实例
            collector: 响应收集器实例
        """
        try:
            logger.info("🔄 注册流式响应收集器...")

            # 检查收集器是否有效
            if collector is None:
                logger.warning("流式响应收集器为空，跳过注册")
                return

            # 检查回调函数是否存在
            if not hasattr(collector, 'callback') or collector.callback is None:
                logger.warning("流式响应收集器回调函数为空，跳过注册")
                return

            # 注册收集器到运行时
            await ClosureAgent.register_closure(
                runtime,
                "stream_collector_agent",
                collector.callback,
                subscriptions=lambda: [
                    TypeSubscription(
                        topic_type=TopicTypes.STREAM_OUTPUT.value,
                        agent_type="stream_collector_agent"
                    )
                ],
            )

            logger.info("✅ 流式响应收集器注册完成")

        except Exception as e:
            logger.error(f"❌ 流式响应收集器注册失败: {str(e)}")
            raise

    async def unregister_agent_from_runtime(self, agent_type: str) -> bool:
        """从运行时注销智能体

        Args:
            agent_type: 智能体类型

        Returns:
            bool: 是否成功注销
        """
        try:
            if agent_type in self._runtime_agents:
                # 从运行时注销（如果运行时支持注销功能）
                if self._runtime and hasattr(self._runtime, 'unregister'):
                    await self._runtime.unregister(agent_type)

                # 从记录中删除
                del self._runtime_agents[agent_type]

                logger.info(f"智能体从运行时注销成功: {agent_type}")
                return True

            return False

        except Exception as e:
            logger.error(f"从运行时注销智能体失败: {agent_type} - {str(e)}")
            return False

    # ===============================重启智能体===============================
    async def restart_agent(self, agent_type: str) -> bool:
        """重启指定的智能体 - 企业级故障恢复功能

        Args:
            agent_type: 要重启的智能体类型

        Returns:
            bool: 重启是否成功
        """
        try:
            logger.info(f"🔄 开始重启智能体: {agent_type}")

            if agent_type not in self._agent_classes:
                logger.error(f"未找到智能体类型: {agent_type}")
                return False

            # 如果有运行时，需要重新注册
            if self._runtime:
                topic_type = None

                # 根据智能体类型确定主题类型
                agent_topic_mapping = {
                    AgentTypes.API_DOC_PARSER.value: TopicTypes.API_DOC_PARSER.value,
                    AgentTypes.API_DATA_PERSISTENCE.value: TopicTypes.API_DATA_PERSISTENCE.value,
                }

                topic_type = agent_topic_mapping.get(agent_type)
                if topic_type:
                    await self.register_agent_to_runtime(
                        runtime=self._runtime,
                        agent_type=agent_type,
                        topic_type=topic_type
                    )

            logger.info(f"✅ 智能体重启成功: {agent_type}")
            return True

        except Exception as e:
            logger.error(f"❌ 智能体重启失败 {agent_type}: {str(e)}")
            return False

    async def restart_all_agents(self) -> Dict[str, bool]:
        """重启所有智能体 - 企业级批量故障恢复功能

        Returns:
            Dict[str, bool]: 每个智能体的重启结果
        """
        restart_results = {}

        try:
            logger.info("🔄 开始重启所有智能体...")

            for agent_type in self._agent_classes.keys():
                restart_results[agent_type] = await self.restart_agent(agent_type)

            successful_restarts = sum(1 for success in restart_results.values() if success)
            total_agents = len(restart_results)

            logger.info(f"✅ 智能体批量重启完成: {successful_restarts}/{total_agents} 成功")

        except Exception as e:
            logger.error(f"❌ 批量重启智能体失败: {str(e)}")

        return restart_results

    async def cleanup(self) -> None:
        """清理资源"""
        import asyncio
        try:
            # 清理运行时注册的智能体
            runtime_agents_to_cleanup = list(self._runtime_agents.keys())
            for agent_type in runtime_agents_to_cleanup:
                try:
                    await self.unregister_agent_from_runtime(agent_type)
                except Exception as e:
                    logger.error(f"清理运行时智能体失败: {agent_type} - {str(e)}")

            # 清理已注册的智能体
            for agent_type, agent_info in self._registered_agents.items():
                try:
                    agent = agent_info["instance"]
                    if hasattr(agent, "cleanup"):
                        if asyncio.iscoroutinefunction(agent.cleanup):
                            await agent.cleanup()
                        else:
                            agent.cleanup()
                except Exception as e:
                    logger.error(f"清理智能体失败: {agent_type} - {str(e)}")

            # 清理内部状态
            self._registered_agents.clear()
            self._runtime_agents.clear()
            self._runtime = None

            logger.info("智能体工厂清理完成")

        except Exception as e:
            logger.error(f"智能体工厂清理失败: {str(e)}")


# 创建全局智能体工厂实例
agent_factory = AgentFactory()
