# @Time: 2025/8/25 14:53
# @Author: lvjing
import logging
import uuid
from datetime import datetime
from typing import Dict

from autogen_core import type_subscription, message_handler, MessageContext
from tortoise.transactions import in_transaction

from apps.ai_api_auto.agents.base_api_agent import BaseApiAgent
from apps.ai_api_auto.models import ApiDetail, ApiParameter, ApiResponse
from apps.ai_api_auto.schemas import ApiDocumentContentParseOutput
from common.agent.types import TopicTypes, AgentTypes

logger = logging.getLogger(__name__)


class ApiDataPersistenceInput:
    """数据持久化输入模型"""

    def __init__(self, parse_result: ApiDocumentContentParseOutput):
        self.session_id = parse_result.session_id
        self.api_info = parse_result.api_info
        self.endpoints = parse_result.endpoints
        self.processing_time = parse_result.processing_time
        # 确保 extended_info 和 raw_parsed_data 可以被 JSON 序列化
        self.extended_info = self._ensure_json_serializable(parse_result.extended_info)
        self.raw_parsed_data = self._ensure_json_serializable(parse_result.raw_parsed_data)
        self.parse_errors = parse_result.parse_errors
        self.parse_warnings = parse_result.parse_warnings

    def _ensure_json_serializable(self, data):
        """确保数据可以被 JSON 序列化"""
        import json
        from pydantic import BaseModel

        def convert_to_dict(obj):
            """递归转换对象为字典"""
            if isinstance(obj, BaseModel):
                # Pydantic 模型转换为字典
                return obj.model_dump()
            elif isinstance(obj, dict):
                # 递归处理字典中的值
                return {k: convert_to_dict(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                # 递归处理列表中的元素
                return [convert_to_dict(item) for item in obj]
            elif hasattr(obj, '__dict__'):
                # 其他对象转换为字典
                return convert_to_dict(obj.__dict__)
            else:
                # 基本类型直接返回
                return obj

        try:
            # 先转换为可序列化的格式
            converted_data = convert_to_dict(data)
            # 测试是否可以序列化
            json.dumps(converted_data)
            return converted_data
        except (TypeError, ValueError) as e:
            # 如果仍然无法序列化，返回字符串表示
            return {"serialization_error": str(e), "data_type": str(type(data)), "data_str": str(data)}


@type_subscription(topic_type=TopicTypes.API_DATA_PERSISTENCE.value)
class ApiDataPersistenceAgent(BaseApiAgent):
    """
    API数据持久化智能体

    负责将API文档解析结果存储到数据库中，
    包括接口信息、参数、响应等详细数据。
    """

    def __init__(self, model_client_instance=None, agent_config=None, **kwargs):
        """初始化API数据持久化智能体"""
        super().__init__(
            agent_type=AgentTypes.API_DATA_PERSISTENCE,
            model_client_instance=model_client_instance,
            **kwargs
        )

        self.agent_config = agent_config or {}

        logger.info(f"API数据持久化智能体初始化完成: {self.agent_name}")

    @message_handler
    async def handle_persistence_request(
            self,
            message: ApiDocumentContentParseOutput,
            ctx: MessageContext
    ) -> None:
        """处理数据持久化请求 - 主要入口点"""
        start_time = datetime.now()

        try:
            logger.info(f"开始存储API数据: {message.file_name}")

            # 确保数据库连接
            await self._ensure_database_connection()

            # 创建输入对象
            persistence_input = ApiDataPersistenceInput(message)

            # 在事务中执行数据存储
            async with in_transaction() as conn:
                # 存储接口信息
                interfaces = await self._store_interfaces(persistence_input, conn)

                # 存储参数信息
                await self._store_parameters(interfaces, persistence_input, conn)

                #  存储响应信息
                await self._store_responses(interfaces, persistence_input, conn)

            # 更新统计指标
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_metrics("data_persistence", True, processing_time)

            logger.info(f"API数据存储完成: {message.file_name}, 接口数: {len(message.endpoints)}")

        except Exception as e:
            self._update_metrics("data_persistence", False)
            error_info = self._handle_common_error(e, "data_persistence")
            logger.error(f"API数据存储失败: {error_info}")

    async def _store_interfaces(
            self,
            persistence_input: ApiDataPersistenceInput,
            conn
    ) -> Dict[str, ApiDetail]:
        """存储接口信息"""
        interfaces = {}

        try:
            for endpoint in persistence_input.endpoints:
                # 使用endpoint_id作为interface_id，确保数据一致性
                interface = await ApiDetail.create(
                    api_id=endpoint.endpoint_id,  # 使用endpoint_id作为interface_id
                    endpoint_id=endpoint.endpoint_id,
                    name=endpoint.summary or f"{endpoint.method} {endpoint.path}",
                    path=endpoint.path,
                    method=endpoint.method,
                    summary=endpoint.summary,
                    description=endpoint.description,
                    api_title=persistence_input.api_info.title,
                    api_version=persistence_input.api_info.version,
                    base_url=persistence_input.api_info.base_url,
                    tags=endpoint.tags,
                    auth_required=endpoint.auth_required,
                    extended_info=persistence_input.extended_info,
                    raw_data=persistence_input.raw_parsed_data,
                    using_db=conn
                )

                interfaces[endpoint.endpoint_id] = interface
                logger.debug(f"存储接口: {interface.name}")

            logger.info(f"存储接口信息完成，共 {len(interfaces)} 个接口")
            return interfaces

        except Exception as e:
            logger.error(f"存储接口信息失败: {str(e)}")
            raise

    async def _store_parameters(
            self,
            interfaces: Dict[str, ApiDetail],
            persistence_input: ApiDataPersistenceInput,
            conn
    ) -> None:
        """存储参数信息"""
        try:
            total_parameters = 0

            for endpoint in persistence_input.endpoints:
                interface = interfaces.get(endpoint.endpoint_id)
                if not interface:
                    continue

                # 删除现有参数记录
                await ApiParameter.filter(api=interface).using_db(conn).delete()

                for param in endpoint.parameters:
                    await ApiParameter.create(
                        parameter_id=str(uuid.uuid4()),
                        api=interface,
                        name=param.name,
                        location=param.location.value,
                        data_type=param.data_type.value,
                        required=param.required,
                        description=param.description,
                        example=str(param.example) if param.example is not None else None,
                        constraints=param.constraints,
                        using_db=conn
                    )
                    total_parameters += 1

            logger.info(f"存储参数信息完成，共 {total_parameters} 个参数")

        except Exception as e:
            logger.error(f"存储参数信息失败: {str(e)}")
            raise

    async def _store_responses(
            self,
            interfaces: Dict[str, ApiDetail],
            persistence_input: ApiDataPersistenceInput,
            conn
    ) -> None:
        """存储响应信息"""
        try:
            total_responses = 0

            for endpoint in persistence_input.endpoints:
                interface = interfaces.get(endpoint.endpoint_id)
                if not interface:
                    continue

                # 删除现有响应记录
                await ApiResponse.filter(api=interface).using_db(conn).delete()

                for response in endpoint.responses:
                    await ApiResponse.create(
                        response_id=str(uuid.uuid4()),
                        api=interface,
                        status_code=response.status_code,
                        description=response.description,
                        content_type=response.content_type,
                        response_schema=response.response_schema,
                        example=response.example,
                        using_db=conn
                    )
                    total_responses += 1

            logger.info(f"存储响应信息完成，共 {total_responses} 个响应")

        except Exception as e:
            logger.error(f"存储响应信息失败: {str(e)}")
            raise
