# @Time: 2025/08/25 10:00
# @Author: lvjing
"""
API文档内容解析智能体

专门负责解析各种格式的API文档，前提是已经处理完成好API相关文档内容，直接传入API文档内容进行处理，并输出标准化数据
"""
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

from autogen_core import type_subscription, message_handler, MessageContext, TopicId

from apps.ai_api_auto.agents.base_api_agent import BaseApiAgent
from apps.ai_api_auto.enums import HttpMethod, ParameterLocation, DataType
from apps.ai_api_auto.prompts.api_agent_prompts import ApiAgentPrompts
from apps.ai_api_auto.schemas import ApiDocumentContentParseInput, ApiDocumentContentParseOutput, ParsedApiInfo, \
    ParsedEndpoint, ApiParameter, ApiResponse
from common.agent.types import TopicTypes, AgentTypes

logger = logging.getLogger(__name__)


@type_subscription(topic_type=TopicTypes.API_DOC_PARSER.value)
class ApiDocParserAgent(BaseApiAgent):
    """
    API文档内容解析智能体
    """

    # 类级别的会话状态管理
    _session_completion_status: Dict[str, Dict[str, Any]] = {}

    def __init__(self, model_client_instance=None, agent_config=None, **kwargs):
        """
        初始化API文件内容解析智能体
        """
        super().__init__(
            agent_type=AgentTypes.API_DOC_PARSER,
            model_client_instance=model_client_instance,
            **kwargs
        )
        self.agent_config = agent_config or {}
        self._initialize_assistant_agent()

        logger.info(f"API文档内容解析智能体初始化完成:{self.agent_name}")

    @message_handler
    async def handle_api_content_parse_request(
            self,
            message: ApiDocumentContentParseInput,
            ctx: MessageContext) -> None:
        """
        处理API文档内容解析请求
        """
        # 执行开始时间
        start_time = datetime.now()
        session_id = message.session_id

        try:
            logger.info(f"开始解析API文档内容: {message.api_file_content[:1000]}")

            # 使用大模型智能解析文档
            parse_result = await self._intelligent_parse_document_content(message.api_file_content)
            logger.info(f"使用大模型智能解析文档内容成功，结果为：{parse_result[:1000]}")

            # 构建解析结果输出
            output = ApiDocumentContentParseOutput(
                session_id=message.session_id,
                api_info=parse_result["api_info"],
                endpoints=parse_result["endpoints"],
                parse_errors=self._format_errors_for_output(parse_result.get("errors", [])),
                parse_warnings=self._format_warnings_for_output(parse_result.get("warnings", [])),
                processing_time=(datetime.now() - start_time).total_seconds(),
                extended_info=parse_result.get("extended_info", {}),
                raw_parsed_data=parse_result.get("raw_parsed_data", {}),
            )

            # 发送结果到数据持久化智能体
            await self._send_to_data_persistence(output, ctx)

            logger.info(f"API文档内容解析完成: {message.api_file_content[:1000]}, 输出内容为：{output}")


        except Exception as e:
            logger.error(f"解析API文档内容解析失败，错误信息：{e}")
            raise

    async def _intelligent_parse_document_content(self, document_content) -> Dict[str, Any]:
        """
        使用大模型智能解析文档内容 - 支持大文档分片处理
        """
        try:
            # 构建解析任务提示词
            task_prompt = ApiAgentPrompts.DOCUMENT_PARSER_TASK_PROMPT.format(document_content=document_content)

            # 使用AssistantAgent进行智能解析
            result_content = await self._run_assistant_agent(task_prompt)
            logger.info(f"使用大模型智能解析文档内容成功，结果为：{result_content}")

            if result_content:
                # 提取JSON结果
                parsed_data = self._extract_json_from_content(result_content)
                if parsed_data:
                    return self._convert_to_standard_format(parsed_data)

        except Exception as e:
            logger.error(f"使用大模型智能解析文档内容失败，错误信息：{e}")
            raise

    def _convert_to_standard_format(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """将解析结果转换为标准格式 - 保留更多有价值信息"""
        try:
            logger.info("开始转换解析结果为标准格式，保留完整信息")

            # 提取API基本信息 - 支持多种字段名
            api_info = self._extract_api_info(parsed_data)

            # 提取端点信息 - 保留更多细节
            endpoints = self._extract_endpoints_with_details(parsed_data)

            # 构建增强的标准格式，保留原始数据的丰富信息
            # 处理错误和警告信息，确保格式正确
            raw_errors = parsed_data.get("errors", parsed_data.get("parsing_issues", []))
            raw_warnings = parsed_data.get("warnings", [])

            standard_result = {
                "api_info": api_info,
                "endpoints": endpoints,
                "errors": self._format_errors_for_output(raw_errors),
                "warnings": self._format_warnings_for_output(raw_warnings),
                "confidence_score": parsed_data.get("confidence_score", 0.8),

                # 保留额外的有价值信息
                "extended_info": {
                    "document_type": parsed_data.get("document_type", "unknown"),
                    "quality_assessment": parsed_data.get("quality_assessment", {}),
                    "testing_recommendations": parsed_data.get("testing_recommendations", []),
                    "error_codes": parsed_data.get("error_codes", {}),
                    "security_schemes": parsed_data.get("security_schemes", {}),
                    "global_headers": parsed_data.get("global_headers", {}),
                    "global_parameters": parsed_data.get("global_parameters", {}),
                    "rate_limiting": parsed_data.get("rate_limiting", {}),
                    "versioning_strategy": parsed_data.get("versioning_strategy", ""),
                    "servers": parsed_data.get("servers", []),
                    "schemas": parsed_data.get("schemas", {}),
                    "parsing_issues": parsed_data.get("parsing_issues", [])
                },

                # 保留原始解析数据的完整副本，供后续智能体使用
                "raw_parsed_data": parsed_data
            }

            logger.info(f"标准格式转换完成，保留了 {len(endpoints)} 个端点和丰富的扩展信息")
            return standard_result

        except Exception as e:
            logger.error(f"转换标准格式失败: {str(e)}")
            # 即使转换失败，也要保留原始数据
            return {
                "api_info": ParsedApiInfo(
                    title=parsed_data.get("title", "Unknown API"),
                    version=parsed_data.get("api_version", "1.0.0"),
                    description=parsed_data.get("description", ""),
                    base_url=parsed_data.get("base_url", "")
                ),
                "endpoints": [],
                "errors": [f"标准格式转换失败: {str(e)}"],
                "warnings": [],
                "confidence_score": 0.5,
                "extended_info": {},
                "raw_parsed_data": parsed_data  # 确保原始数据不丢失
            }

    def _extract_api_info(self, parsed_data: Dict[str, Any]) -> ParsedApiInfo:
        """提取API基本信息 - 支持多种字段名"""
        try:
            # 支持多种可能的字段名
            title = (parsed_data.get("title") or
                     parsed_data.get("api_title") or
                     parsed_data.get("name") or
                     "Unknown API")

            version = (parsed_data.get("api_version") or
                       parsed_data.get("version") or
                       parsed_data.get("info", {}).get("version") or
                       "1.0.0")

            description = (parsed_data.get("description") or
                           parsed_data.get("api_description") or
                           parsed_data.get("info", {}).get("description") or
                           "")

            base_url = (parsed_data.get("base_url") or
                        parsed_data.get("baseUrl") or
                        parsed_data.get("host") or
                        parsed_data.get("servers", [{}])[0].get("url", "") or
                        "")

            # 提取联系信息
            contact = parsed_data.get("contact", {})
            if not contact and "info" in parsed_data:
                contact = parsed_data["info"].get("contact", {})

            # 提取许可证信息
            license_info = parsed_data.get("license", {})
            if not license_info and "info" in parsed_data:
                license_info = parsed_data["info"].get("license", {})

            return ParsedApiInfo(
                title=title,
                version=version,
                description=description,
                base_url=base_url,
                contact=contact,
                license=license_info
            )

        except Exception as e:
            logger.warning(f"提取API基本信息失败: {str(e)}")
            return ParsedApiInfo(
                title="Unknown API",
                version="1.0.0",
                description="",
                base_url=""
            )

    def _extract_endpoints_with_details(self, parsed_data: Dict[str, Any]) -> List[ParsedEndpoint]:
        """提取端点信息 - 保留更多细节"""
        endpoints = []

        try:
            endpoints_data = parsed_data.get("endpoints", [])
            logger.info(f"开始提取 {len(endpoints_data)} 个端点的详细信息")

            for endpoint_data in endpoints_data:
                try:
                    endpoint = self._create_enhanced_parsed_endpoint(endpoint_data)
                    if endpoint:
                        endpoints.append(endpoint)
                        logger.debug(f"成功提取端点: {endpoint.method.value} {endpoint.path}")
                    else:
                        logger.warning(f"端点创建失败: {endpoint_data.get('path', 'unknown')}")

                except Exception as e:
                    logger.warning(f"处理端点失败: {str(e)}, 数据: {endpoint_data}")
                    continue

            logger.info(f"成功提取 {len(endpoints)} 个端点")
            return endpoints

        except Exception as e:
            logger.error(f"提取端点信息失败: {str(e)}")
            return []

    def _create_enhanced_parsed_endpoint(self, endpoint_data: Dict[str, Any]) -> Optional[ParsedEndpoint]:
        """创建增强的解析端点对象 - 保留更多信息"""
        try:
            # 提取基本信息 - 支持多种字段名
            path = (endpoint_data.get("path") or
                    endpoint_data.get("url") or
                    endpoint_data.get("endpoint") or
                    "")

            method_str = (endpoint_data.get("method") or
                          endpoint_data.get("http_method") or
                          endpoint_data.get("verb") or
                          "GET").upper()

            # 验证HTTP方法
            try:
                method = HttpMethod(method_str)
            except ValueError:
                logger.warning(f"无效的HTTP方法: {method_str}, 使用GET作为默认值")
                method = HttpMethod.GET

            # 提取描述信息
            summary = (endpoint_data.get("summary") or
                       endpoint_data.get("title") or
                       endpoint_data.get("name") or
                       "")

            description = (endpoint_data.get("description") or
                           endpoint_data.get("desc") or
                           endpoint_data.get("detail") or
                           "")

            # 提取标签
            tags = endpoint_data.get("tags", [])
            if isinstance(tags, str):
                tags = [tags]
            elif not isinstance(tags, list):
                tags = []

            # 提取参数 - 增强处理
            parameters = self._extract_enhanced_parameters(endpoint_data)

            # 提取响应 - 增强处理
            responses = self._extract_enhanced_responses(endpoint_data)

            # 提取其他信息
            auth_required = endpoint_data.get("auth_required", False)
            deprecated = endpoint_data.get("deprecated", False)
            operation_id = endpoint_data.get("operation_id", endpoint_data.get("operationId", ""))

            # 创建端点对象
            endpoint = ParsedEndpoint(
                path=path,
                method=method,
                summary=summary,
                description=description,
                tags=tags,
                parameters=parameters,
                responses=responses,
                auth_required=auth_required,
                deprecated=deprecated
            )

            # 将额外信息存储在端点对象中（如果支持的话）
            if hasattr(endpoint, 'extended_info'):
                endpoint.extended_info = {
                    "operation_id": operation_id,
                    "request_body": endpoint_data.get("request_body", {}),
                    "security": endpoint_data.get("security", []),
                    "servers": endpoint_data.get("servers", []),
                    "callbacks": endpoint_data.get("callbacks", {}),
                    "examples": endpoint_data.get("examples", {}),
                    "external_docs": endpoint_data.get("externalDocs", {}),
                    "raw_data": endpoint_data  # 保留原始数据
                }

            return endpoint

        except Exception as e:
            logger.error(f"创建增强端点对象失败: {str(e)}")
            return None

    def _extract_enhanced_parameters(self, endpoint_data: Dict[str, Any]) -> List[ApiParameter]:
        """提取增强的参数信息"""
        parameters = []

        try:
            # 从多个可能的字段中提取参数
            params_data = (endpoint_data.get("parameters", []) or
                           endpoint_data.get("params", []) or
                           endpoint_data.get("arguments", []) or
                           [])

            for param_data in params_data:
                try:
                    # 支持多种参数位置表示
                    location_str = (param_data.get("in") or
                                    param_data.get("location") or
                                    param_data.get("place") or
                                    "query")

                    # 映射参数位置
                    location_mapping = {
                        "query": ParameterLocation.QUERY,
                        "header": ParameterLocation.HEADER,
                        "path": ParameterLocation.PATH,
                        "body": ParameterLocation.BODY,
                        "form": ParameterLocation.FORM,
                        "formData": ParameterLocation.FORM,
                        "cookie": ParameterLocation.COOKIE
                    }

                    location = location_mapping.get(location_str.lower(), ParameterLocation.QUERY)

                    # 支持多种数据类型表示
                    type_str = (param_data.get("type") or
                                param_data.get("data_type") or
                                param_data.get("dataType") or
                                "string")

                    # 映射数据类型
                    type_mapping = {
                        "string": DataType.STRING,
                        "integer": DataType.INTEGER,
                        "number": DataType.NUMBER,
                        "boolean": DataType.BOOLEAN,
                        "array": DataType.ARRAY,
                        "object": DataType.OBJECT,
                        "file": DataType.STRING
                    }

                    data_type = type_mapping.get(type_str.lower(), DataType.STRING)

                    parameter = ApiParameter(
                        name=param_data.get("name", ""),
                        location=location,
                        data_type=data_type,
                        required=param_data.get("required", False),
                        description=param_data.get("description", ""),
                        example=param_data.get("example"),
                        constraints=param_data.get("constraints", {})
                    )
                    parameters.append(parameter)

                except Exception as e:
                    logger.warning(f"处理参数失败: {str(e)}, 参数数据: {param_data}")
                    continue

            # 处理请求体参数
            request_body = endpoint_data.get("request_body", {})
            if request_body:
                body_param = ApiParameter(
                    name="body",
                    location=ParameterLocation.BODY,
                    data_type=DataType.OBJECT,
                    required=request_body.get("required", False),
                    description=request_body.get("description", "请求体"),
                    example=request_body.get("example"),
                    constraints={
                        "content_type": request_body.get("content_type", "application/json"),
                        "schema": request_body.get("schema", {})
                    }
                )
                parameters.append(body_param)

            return parameters

        except Exception as e:
            logger.error(f"提取参数信息失败: {str(e)}")
            return []

    def _extract_enhanced_responses(self, endpoint_data: Dict[str, Any]) -> List[ApiResponse]:
        """提取增强的响应信息"""
        responses = []

        try:
            responses_data = endpoint_data.get("responses", {})

            for status_code, response_info in responses_data.items():
                try:
                    response = ApiResponse(
                        status_code=str(status_code),
                        description=response_info.get("description", ""),
                        content_type=response_info.get("content_type", "application/json"),
                        response_schema=response_info.get("schema", {}),
                        example=response_info.get("example")
                    )
                    responses.append(response)

                except Exception as e:
                    logger.warning(f"处理响应失败: {str(e)}, 响应数据: {response_info}")
                    continue

            # 如果没有响应定义，添加默认响应
            if not responses:
                default_response = ApiResponse(
                    status_code="200",
                    description="成功响应",
                    content_type="application/json",
                    response_schema={},
                    example=None
                )
                responses.append(default_response)

            return responses

        except Exception as e:
            logger.error(f"提取响应信息失败: {str(e)}")
            return []

    def _format_errors_for_output(self, errors: List) -> List[str]:
        """格式化错误信息为字符串列表"""
        formatted_errors = []

        try:
            for error in errors:
                if isinstance(error, dict):
                    # 如果是结构化错误对象，转换为描述性字符串
                    level = error.get("level", "error")
                    message = error.get("message", "")
                    location = error.get("location", "")
                    suggestion = error.get("suggestion", "")

                    error_str = f"[{level.upper()}]"
                    if location:
                        error_str += f" {location}:"
                    error_str += f" {message}"
                    if suggestion:
                        error_str += f" (建议: {suggestion})"

                    formatted_errors.append(error_str)
                elif isinstance(error, str):
                    # 如果已经是字符串，直接使用
                    formatted_errors.append(error)
                else:
                    # 其他类型，转换为字符串
                    formatted_errors.append(str(error))

        except Exception as e:
            logger.warning(f"格式化错误信息失败: {str(e)}")
            # 如果格式化失败，至少保留原始信息的字符串表示
            formatted_errors = [str(error) for error in errors]

        return formatted_errors

    def _format_warnings_for_output(self, warnings: List) -> List[str]:
        """格式化警告信息为字符串列表"""
        formatted_warnings = []

        try:
            for warning in warnings:
                if isinstance(warning, dict):
                    # 如果是结构化警告对象，转换为描述性字符串
                    level = warning.get("level", "warning")
                    message = warning.get("message", "")
                    location = warning.get("location", "")
                    suggestion = warning.get("suggestion", "")

                    warning_str = f"[{level.upper()}]"
                    if location:
                        warning_str += f" {location}:"
                    warning_str += f" {message}"
                    if suggestion:
                        warning_str += f" (建议: {suggestion})"

                    formatted_warnings.append(warning_str)
                elif isinstance(warning, str):
                    # 如果已经是字符串，直接使用
                    formatted_warnings.append(warning)
                else:
                    # 其他类型，转换为字符串
                    formatted_warnings.append(str(warning))

        except Exception as e:
            logger.warning(f"格式化警告信息失败: {str(e)}")
            # 如果格式化失败，至少保留原始信息的字符串表示
            formatted_warnings = [str(warning) for warning in warnings]

        return formatted_warnings

    async def _send_to_data_persistence(self, output: ApiDocumentContentParseOutput, ctx: MessageContext):
        """发送解析结果到数据持久化智能体"""
        try:
            # 发送到数据持久化智能体
            await self.runtime.publish_message(
                output,
                topic_id=TopicId(type=TopicTypes.API_DATA_PERSISTENCE.value, source=self.agent_name)
            )

            logger.info(f"已发送解析结果到数据持久化智能体: {output.document_id}")

        except Exception as e:
            logger.error(f"发送到数据持久化智能体失败: {str(e)}")
