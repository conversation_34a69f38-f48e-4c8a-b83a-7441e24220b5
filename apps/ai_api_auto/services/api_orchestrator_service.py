# @Time: 2025/8/26 08:57
# @Author: lvjing
"""
接口自动化智能体编排服务
负责协调各个智能体的工作流程，使用新的数据模型
"""
import logging
from datetime import datetime
from typing import Optional, Dict, Any

from autogen_core import SingleThreadedAgentRuntime, TopicId

from apps.ai_api_auto.factory import agent_factory
from apps.ai_api_auto.schemas import ApiDocumentContentParseInput
from common.agent.stream_response_collector import StreamResponseCollector
from common.agent.types import AgentPlatform, TopicTypes

logger = logging.getLogger(__name__)


class ApiAutomationOrchestrator:
    """
    接口自动化智能体编排器

    负责协调以下智能体的工作流程：
    1. API文档解析智能体 - 解析API文档，输出 DocumentParseOutput

    数据流转：DocumentParseInput
    """

    def __init__(self, collector: Optional[StreamResponseCollector] = None):
        """
        初始化接口自动化编排器

        Args:
            collector: 可选的StreamResponseCollector用于捕获智能体响应
        """
        self.response_collector = collector or StreamResponseCollector(
            platform=AgentPlatform.API_AUTOMATION
        )
        self.runtime: Optional[SingleThreadedAgentRuntime] = None
        self.agent_factory = agent_factory
        self.active_sessions: Dict[str, Dict[str, Any]] = {}

        # 编排器性能指标
        self.orchestrator_metrics = {
            "total_workflows": 0,
            "successful_workflows": 0,
            "failed_workflows": 0,
            "active_sessions": 0
        }

        logger.info("接口自动化智能体编排器初始化完成")

    async def initialize(self, **agent_kwargs) -> None:
        """
        初始化编排器和智能体

        Args:
            **agent_kwargs: 智能体初始化参数
        """
        try:
            logger.info("🚀 初始化接口自动化智能体编排器...")

            if self.runtime is None:
                # 创建运行时
                self.runtime = SingleThreadedAgentRuntime()

                # 注册智能体到运行时
                await self.agent_factory.register_agents_to_runtime(self.runtime)

                # 设置响应收集器
                await self.agent_factory.register_stream_collector(
                    runtime=self.runtime,
                    collector=self.response_collector
                )

                # 启动运行时
                self.runtime.start()

                logger.info("✅ 接口自动化智能体编排器初始化完成")

        except Exception as e:
            logger.error(f"❌ 接口自动化智能体编排器初始化失败: {str(e)}")
            raise

    async def process_api_document_content(
            self,
            session_id: str,
            api_file_content: str,
            config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        处理API文档内容
        """
        try:
            self.orchestrator_metrics["total_workflows"] += 1
            self.orchestrator_metrics["active_sessions"] += 1

            # 记录会话信息
            self.active_sessions[session_id] = {
                "start_time": datetime.now(),
                "status": "processing",
                "current_step": "document_parsing",
                "api_file_content": api_file_content[:1000],
                "config": config or {}
            }

            logger.info(f"开始处理API文档内容: {api_file_content[:1000]} (会话: {session_id})")

            # 解析API文档内容
            await self._parse_api_document_content(
                session_id, api_file_content
            )

            # 更新会话状态
            self.active_sessions[session_id]["current_step"] = "completed"
            self.active_sessions[session_id]["status"] = "completed"
            self.active_sessions[session_id]["end_time"] = datetime.now()

            self.orchestrator_metrics["successful_workflows"] += 1
            self.orchestrator_metrics["active_sessions"] -= 1

            return {
                "success": True,
                "session_id": session_id,
                "message": "API文档处理完成",
                "session_info": self.active_sessions[session_id],
                "note": "完整的工作流程包括：文档解析 → 接口分析 → 测试用例生成 → 脚本生成"
            }

        except Exception as e:
            self.orchestrator_metrics["failed_workflows"] += 1
            self.orchestrator_metrics["active_sessions"] -= 1

            # 创建会话信息
            self.active_sessions[session_id] = {
                "status": "failed",
                "error": str(e),
                "end_time": datetime.now()
            }

            logger.error(f"处理API文档内容失败: {str(e)}")
            raise

    async def _parse_api_document_content(self, session_id: str, api_file_content: str):
        """
        发送API文档呢日解析请求
        """
        try:
            # 构建解析请求
            parse_request = ApiDocumentContentParseInput(
                session_id=session_id,
                api_file_content=api_file_content
            )
            # 发送到API文档解析智能体
            await self.runtime.publish_message(
                parse_request,
                topic_id=TopicId(type=TopicTypes.API_DOC_PARSER.value, source="api_orchestrator")
            )
        except Exception as e:
            logger.error(f"发送API文档解析请求失败: {str(e)}")
            raise

    async def cleanup(self) -> None:
        """清理编排器资源"""
        try:
            # 清理智能体
            await self.agent_factory.cleanup()

            # 清理响应收集器
            if self.response_collector:
                self.response_collector.cleanup()

            # 停止运行时
            if self.runtime:
                await self.runtime.stop()

            # 清理会话
            self.active_sessions.clear()

            logger.info("接口自动化编排器资源清理完成")

        except Exception as e:
            logger.error(f"清理编排器资源失败: {str(e)}")
