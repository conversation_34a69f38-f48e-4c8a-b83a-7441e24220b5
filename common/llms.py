import logging

from autogen_ext.models.openai import OpenAIChatCompletionClient

logger = logging.getLogger(__name__)


def _setup_vllm_model_client():
    """设置模型客户端"""
    # 个人lijing Key 本地测试请替换，后面有多模态模型部署再调整配置
    model_config = {"model": "qwen-vl-max-latest", "api_key": "sk-84978b394a644b2698d9708d957233fe", "model_info": {
        "vision": True,
        "function_calling": True,
        "json_output": True,
        "family": "unknown",
        "multiple_system_messages": True,
        "structured_output": True
    }, "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1"}

    return OpenAIChatCompletionClient(**model_config)


model_client = _setup_vllm_model_client()


def get_deepseek_model_client(max_tokens: int = 8192) -> OpenAIChatCompletionClient:
    """获取DeepSeek模型客户端"""
    try:
        client = OpenAIChatCompletionClient(
            model="deepseek-ai/DeepSeek-V3",
            api_key="sk-dmrvmhxznttkfrpfgjostxlsjytrgiteisnytayxaqeqijwo",
            base_url="https://api.siliconflow.cn/v1",
            max_tokens=max_tokens,
            model_info={
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "structured_output": True,  # 添加 structured_output 字段
                "family": "unknown",
                "multiple_system_messages": True
            }
        )
        logger.info("获取DeepSeek模型客户端成功")
        return client
    except Exception as e:
        raise


def get_model_client(model_type: str = "deepseek", max_tokens: int = 8192) -> OpenAIChatCompletionClient:
    """根据类型获取模型客户端"""
    if model_type.lower() == "deepseek":
        return get_deepseek_model_client(max_tokens=max_tokens)
